#!/usr/bin/env python3
"""
Príklad použitia precompute_features.py s 1h indikátormi.
Tento skript ukazuje ako spustiť predpočítanie features s pridanými 1h indikátormi.
"""

import subprocess
import sys
from pathlib import Path

def run_precompute_with_1h_indicators():
    """
    Spustí precompute_features.py s povolenými 1h indikátormi.
    """
    
    # Konfigurácia
    config_path = "temp_config_1s.json"  # alebo iný config súbor
    raw_data_dir = "raw_data"
    output_dir = "parquet_processed"
    start_date = "2024-04-01"
    end_date = "2024-04-30"
    
    # Kontrola existencie súborov
    if not Path(config_path).exists():
        print(f"❌ Config súbor neexistuje: {config_path}")
        print("Prosím, vytvorte config súbor alebo upravte cestu v skripte.")
        return False
    
    if not Path(raw_data_dir).exists():
        print(f"❌ Raw data adresár neexistuje: {raw_data_dir}")
        print("Prosím, vytvorte adresár s raw dátami alebo upravte cestu v skripte.")
        return False
    
    # Príkaz na spustenie
    cmd = [
        sys.executable, "precompute_features.py",
        "--config", config_path,
        "--raw-data-dir", raw_data_dir,
        "--output-dir", output_dir,
        "--start", start_date,
        "--end", end_date,
        "--enable-1h-indicators"  # Toto je nová možnosť!
    ]
    
    print("🚀 Spúšťam precompute_features.py s 1h indikátormi...")
    print(f"Príkaz: {' '.join(cmd)}")
    print()
    
    try:
        # Spustenie príkazu
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ Predpočítanie features s 1h indikátormi dokončené!")
        print("\nVýstup:")
        print(result.stdout)
        
        if result.stderr:
            print("\nWarnings/Errors:")
            print(result.stderr)
            
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Chyba pri spúšťaní: {e}")
        print(f"Výstup: {e.stdout}")
        print(f"Chyby: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ precompute_features.py súbor nebol nájdený v aktuálnom adresári")
        return False

def show_1h_indicators_info():
    """
    Zobrazí informácie o 1h indikátoroch, ktoré budú pridané.
    """
    print("📊 1h indikátory, ktoré budú pridané:")
    print("=" * 50)
    
    indicators = [
        ("ATR_14_1h", "Average True Range na 1h timeframe"),
        ("RSI_14_1h", "Relative Strength Index na 1h timeframe"),
        ("EMA_9_1h", "Exponential Moving Average (9) na 1h timeframe"),
        ("EMA_21_1h", "Exponential Moving Average (21) na 1h timeframe"),
        ("ADX_14_1h", "Average Directional Index na 1h timeframe"),
        ("DMP_14_1h", "Directional Movement Plus na 1h timeframe"),
        ("DMN_14_1h", "Directional Movement Minus na 1h timeframe"),
        ("bollinger_bands_*_1h", "Bollinger Bands na 1h timeframe"),
        ("vwap_1h", "Volume Weighted Average Price na 1h timeframe"),
        ("hmm_state_*_1h", "Hidden Markov Model state na 1h timeframe")
    ]
    
    for name, description in indicators:
        print(f"  • {name:<25} - {description}")
    
    print()
    print("💡 Tieto indikátory poskytujú dlhodobejší kontext pre trading rozhodnutia")
    print("   a môžu pomôcť identifikovať trendy na vyšších timeframe-och.")
    print()

if __name__ == "__main__":
    print("🔧 Precompute Features s 1h indikátormi")
    print("=" * 50)
    print()
    
    show_1h_indicators_info()
    
    # Spýtať sa používateľa
    response = input("Chcete spustiť predpočítanie features s 1h indikátormi? (y/n): ")
    
    if response.lower() in ['y', 'yes', 'ano', 'a']:
        success = run_precompute_with_1h_indicators()
        if success:
            print("\n🎉 Hotovo! Features s 1h indikátormi boli úspešne predpočítané.")
        else:
            print("\n💥 Nastala chyba pri predpočítaní features.")
    else:
        print("\n👋 Predpočítanie zrušené.")
        
    print("\n📝 Poznámka: Pre manuálne spustenie použite:")
    print("python precompute_features.py --config <config> --raw-data-dir <dir> --output-dir <dir> --start <date> --end <date> --enable-1h-indicators")
