{
  "symbol": "XRPUSDC",
  "primaryTimeframe": "1s",
  "featureParquetDir": "parquet_processed",
  "use_1s_decisions": true,
  "strict_features": true,
  
  "dataProvider": {
    "symbol": "XRPUSDC"
  },

  "coinapi": {
    "apiKey": "${COINAPI_KEY}"
  },

  "binance": {
    "apiKey": "${BINANCE_API_KEY}",
    "apiSecret": "${BINANCE_API_SECRET}",
    "futures": true,
    "leverage": 10,
    "marginType": "ISOLATED"
  },

  "indicatorSettings": {
    "stateLookback": 30,

    "orderFlowFeatures": {
      "enabled": true,
      "sourceTf": "trades",
      "volumeDeltaPeriods": ["30s", "2m"],
      "tradeCountDeltaPeriods": ["100t", "500t"],
      "cvdEnabled": true,
      "cvdResetFrequency": "daily"
    },

    "_comment_5m": "=== 5-minute timeframe indicators ===",
    "hmm_5m":  { "enabled": true, "tf": "5m", "n_components": 3, "window": 200, "data_source": "volatility" },
    "atr_5m":  { "enabled": true, "tf": "5m", "basePeriod": 14 },
    "vwap_5m": { "enabled": true, "tf": "5m" },
    "volume":  { "enabled": true },
    "volumeImbalance_5m": { "enabled": true, "tf": "5m" },
    "bollinger_5m": { "enabled": true, "tf": "5m", "basePeriod": 20, "baseStDev": 2.0 },
    "adx":     { "enabled": true, "tf": "5m", "basePeriod": 14 },
    "rsi_5m":  { "enabled": true, "tf": "5m", "basePeriod": 14 },
    "ema_5m":  { "enabled": true, "tf": "5m", "baseFastPeriod": 9, "baseSlowPeriod": 21 },

    "_comment_1h": "=== 1-hour timeframe indicators ===",
    "atr_1h":  { "enabled": true, "tf": "1h", "basePeriod": 14 },
    "rsi_1h":  { "enabled": true, "tf": "1h", "basePeriod": 14 },
    "ema_1h":  { "enabled": true, "tf": "1h", "baseFastPeriod": 9, "baseSlowPeriod": 21 },
    "adx_1h":  { "enabled": true, "tf": "1h", "basePeriod": 14 },
    "bollinger_1h": { "enabled": true, "tf": "1h", "basePeriod": 20, "baseStDev": 2.0 },
    "vwap_1h": { "enabled": true, "tf": "1h" },
    "hmm_1h":  { "enabled": true, "tf": "1h", "n_components": 3, "window": 200, "data_source": "volatility" }
  },

  "envSettings": {
    "feature_columns": [
      "close", "volume", "open", "high", "low",
      
      "_comment_5m_features": "5-minute timeframe features",
      "ATR_14", "RSI_14", "EMA_9", "EMA_21", "ADX_14", "DMP_14", "DMN_14",
      "bollinger_bands_upper_20_2.0", "bollinger_bands_middle_20_2.0", 
      "bollinger_bands_lower_20_2.0", "bollinger_bands_width_20_2.0",
      "vwap", "VWAP_pta", "hmm_state_3c_volatility_5m",
      
      "_comment_1h_features": "1-hour timeframe features",
      "ATR_14_1h", "RSI_14_1h", "EMA_9_1h", "EMA_21_1h", "ADX_14_1h", "DMP_14_1h", "DMN_14_1h",
      "bollinger_bands_upper_20_2.0_1h", "bollinger_bands_middle_20_2.0_1h",
      "bollinger_bands_lower_20_2.0_1h", "bollinger_bands_width_20_2.0_1h",
      "vwap_1h", "hmm_state_3c_volatility_1h",
      
      "_comment_orderbook": "Orderbook features",
      "spread", "mid_price", "tob_imbalance", "depth_imbalance5", "depth_slope5",
      "ob_bid_vol_l1", "ob_ask_vol_l1", "ob_bid_vol_l2", "ob_ask_vol_l2",
      "ob_bid_vol_l3", "ob_ask_vol_l3", "ob_bid_vol_l4", "ob_ask_vol_l4",
      "ob_bid_vol_l5", "ob_ask_vol_l5", "dvol_bid_l1", "dvol_ask_l1",
      
      "_comment_orderflow": "Order flow features",
      "volume_delta_30s", "volume_delta_2m", "trade_count_delta_100t",
      "trade_count_delta_500t", "cvd_reset_daily", "trade_dir_sum_1s",
      "trade_skew_1s", "dt_since_buy", "dt_since_sell",
      
      "_comment_volume": "Volume features",
      "buy_volume", "sell_volume", "trade_count"
    ]
  },

  "tradeParams": {
    "slippagePercentage": 0.02,
    "feePercentage": 0.05,
    "entryActionThreshold": 0.9,
    "exitActionThreshold": 0.9,
    "longEntryThreshold": 0.9,
    "shortEntryThreshold": 0.9,
    "minAtrPercentOfPrice": 0.0,
    "minSLDistanceATR": 1.0,
    "minSLDistancePercent": 1.2,
    "rrTarget": 2.0,
    "tpMode": "capitalPercentage",
    "tpCapitalPercentage": 1.5,
    "agentExitsEnabled": false,
    "minTimeInTradeSeconds": 600,
    "enableDynamicThresholds": true,
    "volatilityAdjustmentFactor": 0.15,
    "maxVolatilityThreshold": 0.005,
    "_comment": "Config with both 5m and 1h indicators for comprehensive market analysis"
  },
  
  "blackoutHours": [],
  
  "account": {
    "initialEquity": 100
  },
  
  "testMode": false,
  
  "trailingStopLoss": {
    "enabled": false,
    "activateATRMultiplier": 1,
    "trailATRMultiplier": 0.35
  }
}
