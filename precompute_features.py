#!/usr/bin/env python3
"""
Script na predpočítanie features pre simuláciu tradingu.
Spracováva surové OHLCV, orderbook a trade dáta na features potrebné pre agent.
"""

import json
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime, timezone
import numpy as np
from indicators import calculate_and_merge_indicators
import warnings

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_ohlc_data(df: pd.DataFrame) -> tuple:
    """
    Validuje OHLC dáta a vráti počet neplatných hodnôt.
    
    Args:
        df: DataFrame s OHLC dátami
        
    Returns:
        tuple: (invalid_open, invalid_high, invalid_low, invalid_close)
    """
    invalid_open = (df['open'] <= 0).sum()
    invalid_high = (df['high'] <= 0).sum()
    invalid_low = (df['low'] <= 0).sum()
    invalid_close = (df['close'] <= 0).sum()
    
    return invalid_open, invalid_high, invalid_low, invalid_close

def fix_ohlc_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Opraví neplatné OHLC dáta pomocou forward-fill a OHLC vztahov.
    
    Args:
        df: DataFrame s OHLC dátami
        
    Returns:
        DataFrame s opravenými dátami
    """
    df = df.copy()
    
    # Najprv forward-fill pre všetky stĺpce
    df['open'] = df['open'].replace(0, np.nan).ffill()
    df['high'] = df['high'].replace(0, np.nan).ffill()
    df['low'] = df['low'].replace(0, np.nan).ffill()
    df['close'] = df['close'].replace(0, np.nan).ffill()
    
    # Ak stále existujú NaN hodnoty na začiatku, použijeme backward-fill
    df['open'] = df['open'].bfill()
    df['high'] = df['high'].bfill()
    df['low'] = df['low'].bfill()
    df['close'] = df['close'].bfill()
    
    # Validácia OHLC vztahov
    # High musí byť >= max(open, close)
    df['high'] = np.maximum(df['high'], np.maximum(df['open'], df['close']))
    
    # Low musí byť <= min(open, close)
    df['low'] = np.minimum(df['low'], np.minimum(df['open'], df['close']))
    
    # Volume a count nesmú byť záporné
    if 'volume' in df.columns:
        df['volume'] = df['volume'].clip(lower=0)
    if 'count' in df.columns:
        df['count'] = df['count'].clip(lower=0)
    
    return df

def check_and_fix_data_quality(df: pd.DataFrame, file_path: str = "unknown") -> pd.DataFrame:
    """
    Skontroluje a opraví kvalitu OHLC dát.
    
    Args:
        df: DataFrame s OHLC dátami
        file_path: Cesta k súboru pre logovanie
        
    Returns:
        DataFrame s opravenými dátami
    """
    # Kontrola pred opravou
    invalid_before = validate_ohlc_data(df)
    
    if sum(invalid_before) > 0:
        logger.warning(f"Nájdené neplatné OHLC hodnoty v {file_path}:")
        logger.warning(f"  open: {invalid_before[0]} neplatných hodnôt")
        logger.warning(f"  high: {invalid_before[1]} neplatných hodnôt")
        logger.warning(f"  low: {invalid_before[2]} neplatných hodnôt")
        logger.warning(f"  close: {invalid_before[3]} neplatných hodnôt")
        
        # Oprava dát
        df_fixed = fix_ohlc_data(df)
        
        # Kontrola po oprave
        invalid_after = validate_ohlc_data(df_fixed)
        
        if sum(invalid_after) == 0:
            logger.info(f"✅ Všetky neplatné OHLC hodnoty boli opravené v {file_path}")
        else:
            logger.warning(f"⚠️ Zostávajúce neplatné hodnoty v {file_path}:")
            logger.warning(f"  open: {invalid_after[0]}, high: {invalid_after[1]}, low: {invalid_after[2]}, close: {invalid_after[3]}")
        
        return df_fixed
    else:
        logger.info(f"✅ Žiadne neplatné OHLC hodnoty v {file_path}")
        return df

def fill_missing_features(features_df: pd.DataFrame, expected_features: list) -> pd.DataFrame:
    """
    Doplní chýbajúce features z konfigurácie pomocou placeholder hodnôt.
    
    Args:
        features_df: DataFrame s existujúcimi features
        expected_features: Zoznam všetkých features z konfigurácie
        
    Returns:
        DataFrame s všetkými očakávanými features
    """
    # Zoznam orderbook features, ktoré nastavíme na 0
    orderbook_features = [
        "spread", "mid_price", "tob_imbalance", "depth_imbalance5", "depth_slope5",
        "ob_bid_vol_l1", "ob_ask_vol_l1", "ob_bid_vol_l2", "ob_ask_vol_l2",
        "ob_bid_vol_l3", "ob_ask_vol_l3", "ob_bid_vol_l4", "ob_ask_vol_l4",
        "ob_bid_vol_l5", "ob_ask_vol_l5", "dvol_bid_l1", "dvol_ask_l1"
    ]
    
    # Zoznam order flow features, ktoré nastavíme na 0
    order_flow_features = [
        "volume_delta_30s", "volume_delta_2m", "trade_count_delta_100t",
        "trade_count_delta_500t", "cvd_reset_daily", "trade_dir_sum_1s",
        "trade_skew_1s", "dt_since_buy", "dt_since_sell"
    ]
    
    # Trade volume features - použijeme základný volume ak chýbajú
    if "buy_volume" not in features_df.columns:
        features_df["buy_volume"] = features_df["volume"] * 0.5
    if "sell_volume" not in features_df.columns:
        features_df["sell_volume"] = features_df["volume"] * 0.5
    if "trade_count" not in features_df.columns:
        features_df["trade_count"] = 100  # Konštantná hodnota
    
    # Doplnenie chýbajúcích orderbook features
    for feature in orderbook_features:
        if feature not in features_df.columns:
            if feature == "spread":
                features_df[feature] = 0.0001  # Malý spread
            elif feature == "mid_price":
                features_df[feature] = features_df["close"]
            else:
                features_df[feature] = 0.0
        else:
            # Ak feature existuje ale má NaN, nahradíme vhodnou hodnotou
            if feature == "spread":
                features_df[feature] = features_df[feature].fillna(0.0001)
            elif feature == "mid_price":
                features_df[feature] = features_df[feature].fillna(features_df["close"])
            else:
                features_df[feature] = features_df[feature].fillna(0.0)
    
    # Doplnenie chýbajúcich order flow features
    for feature in order_flow_features:
        if feature not in features_df.columns:
            features_df[feature] = 0.0
        else:
            # Ak feature existuje ale má NaN, nahradíme nulou
            features_df[feature] = features_df[feature].fillna(0.0)
    
    # Doplnenie HMM state ak chýba
    if "hmm_state_3c_volatility_5m" not in features_df.columns:
        features_df["hmm_state_3c_volatility_5m"] = 1  # Neurálny stav
    else:
        features_df["hmm_state_3c_volatility_5m"] = features_df["hmm_state_3c_volatility_5m"].fillna(1)
    
    # NENAHRÁDZAME technické indikátory nulami! Iba forward-fill
    technical_indicators = [
        # 5m timeframe indicators
        "ATR_14", "RSI_14", "EMA_9", "EMA_21", "ADX_14", "DMP_14", "DMN_14",
        "bollinger_bands_upper_20_2.0", "bollinger_bands_middle_20_2.0",
        "bollinger_bands_lower_20_2.0", "bollinger_bands_width_20_2.0", "vwap",
        "supertrend_10_3.0", "supertrend_direction_10_3.0",
        # 1h timeframe indicators
        "ATR_14_1h", "RSI_14_1h", "EMA_9_1h", "EMA_21_1h", "ADX_14_1h", "DMP_14_1h", "DMN_14_1h",
        "bollinger_bands_upper_20_2.0_1h", "bollinger_bands_middle_20_2.0_1h",
        "bollinger_bands_lower_20_2.0_1h", "bollinger_bands_width_20_2.0_1h", "vwap_1h",
        "supertrend_10_3.0_1h", "supertrend_direction_10_3.0_1h"
    ]
    
    # Pre technické indikátory použijeme forward-fill, backward-fill a rozumné default hodnoty
    for indicator in technical_indicators:
        if indicator in features_df.columns:
            nan_count_before = features_df[indicator].isna().sum()
            if nan_count_before > 0:
                # Najprv forward-fill
                features_df[indicator] = features_df[indicator].ffill()
                # Potom backward-fill pre začiatočné NaN
                features_df[indicator] = features_df[indicator].bfill()
                nan_count_after = features_df[indicator].isna().sum()
                logger.info(f"Technický indikátor {indicator}: {nan_count_before} -> {nan_count_after} NaN")

                # Ak stále zostávajú NaN, použijeme rozumnú default hodnotu
                if nan_count_after > 0:
                    if "RSI" in indicator:
                        features_df[indicator] = features_df[indicator].fillna(50.0)
                    elif "ATR" in indicator:
                        features_df[indicator] = features_df[indicator].fillna(features_df["close"] * 0.01)
                    elif "EMA" in indicator or "vwap" in indicator:
                        features_df[indicator] = features_df[indicator].fillna(features_df["close"])
                    elif "bollinger" in indicator:
                        if "upper" in indicator:
                            features_df[indicator] = features_df[indicator].fillna(features_df["close"] * 1.02)
                        elif "lower" in indicator:
                            features_df[indicator] = features_df[indicator].fillna(features_df["close"] * 0.98)
                        elif "width" in indicator:
                            features_df[indicator] = features_df[indicator].fillna(features_df["close"] * 0.04)
                        else:  # middle
                            features_df[indicator] = features_df[indicator].fillna(features_df["close"])
                    elif "ADX" in indicator or "DMP" in indicator or "DMN" in indicator:
                        features_df[indicator] = features_df[indicator].fillna(25.0)
                    elif "supertrend_direction" in indicator:
                        # SuperTrend direction: 1 = bullish, -1 = bearish
                        features_df[indicator] = features_df[indicator].fillna(1.0)
                    elif "supertrend" in indicator:
                        # SuperTrend value: použijeme close cenu ako default
                        features_df[indicator] = features_df[indicator].fillna(features_df["close"])
                    else:
                        features_df[indicator] = features_df[indicator].fillna(0.0)
                    logger.info(f"Použité default hodnoty pre {indicator}")
        else:
            # Ak technický indikátor úplne chýba, vytvoríme ho s rozumnou hodnotou
            logger.warning(f"Technický indikátor {indicator} úplne chýba, vytváram default hodnoty")
            if "RSI" in indicator:
                features_df[indicator] = 50.0
            elif "ATR" in indicator:
                features_df[indicator] = features_df["close"] * 0.01
            elif "EMA" in indicator or "vwap" in indicator:
                features_df[indicator] = features_df["close"]
            elif "bollinger" in indicator:
                if "upper" in indicator:
                    features_df[indicator] = features_df["close"] * 1.02
                elif "lower" in indicator:
                    features_df[indicator] = features_df["close"] * 0.98
                elif "width" in indicator:
                    features_df[indicator] = features_df["close"] * 0.04
                else:  # middle
                    features_df[indicator] = features_df["close"]
            elif "ADX" in indicator or "DMP" in indicator or "DMN" in indicator:
                features_df[indicator] = 25.0
            elif "supertrend_direction" in indicator:
                # SuperTrend direction: 1 = bullish, -1 = bearish
                features_df[indicator] = 1.0
            elif "supertrend" in indicator:
                # SuperTrend value: použijeme close cenu ako default
                features_df[indicator] = features_df["close"]
            else:
                features_df[indicator] = 0.0
    
    # Špeciálne ošetrenie pre VWAP_pta - ak má všetky NaN, nahradíme close cenou
    if "VWAP_pta" in features_df.columns:
        nan_count = features_df["VWAP_pta"].isna().sum()
        if nan_count > 0:
            if nan_count == len(features_df):
                # Všetky NaN - trade dáta chýbajú úplne, použijeme close ako proxy
                features_df["VWAP_pta"] = features_df["close"]
                logger.info(f"VWAP_pta: nahradené všetky {nan_count} NaN hodnotami close ceny (trade dáta chýbajú)")
            else:
                # Čiastočné NaN - forward-fill a backward-fill
                features_df["VWAP_pta"] = features_df["VWAP_pta"].ffill().bfill()
                remaining_nan = features_df["VWAP_pta"].isna().sum()
                if remaining_nan > 0:
                    features_df["VWAP_pta"] = features_df["VWAP_pta"].fillna(features_df["close"])
                logger.info(f"VWAP_pta: forward/backward-fill {nan_count} -> {remaining_nan} NaN")
    else:
        # Ak VWAP_pta úplne chýba, vytvoríme ho z close ceny
        features_df["VWAP_pta"] = features_df["close"]
        logger.info("VWAP_pta: vytvorený z close ceny (úplne chýbal)")
    
    # Pre ostatné features (nie technické indikátory) môžeme použiť nuly
    for col in features_df.columns:
        if col not in technical_indicators and features_df[col].isna().any():
            if col in expected_features:
                logger.info(f"Nahrádzam NaN hodnoty v non-technical feature {col}")
                features_df[col] = features_df[col].fillna(0.0)
    
    # Vytvorenie všetkých chýbajúcich features
    missing_features = [f for f in expected_features if f not in features_df.columns]
    if missing_features:
        logger.warning(f"Vytváram {len(missing_features)} chýbajúcich features: {missing_features[:10]}...")
        for feature in missing_features:
            if feature in orderbook_features:
                if feature == "spread":
                    features_df[feature] = 0.0001
                elif feature == "mid_price":
                    features_df[feature] = features_df["close"]
                else:
                    features_df[feature] = 0.0
            elif feature in order_flow_features:
                features_df[feature] = 0.0
            elif feature == "hmm_state_3c_volatility_5m":
                features_df[feature] = 1
            else:
                features_df[feature] = 0.0

    # Finálna kontrola a odstránenie všetkých NaN hodnôt
    logger.info("Finálna kontrola NaN hodnôt...")
    for feature in expected_features:
        if feature in features_df.columns:
            nan_count = features_df[feature].isna().sum()
            if nan_count > 0:
                logger.warning(f"Finálne čistenie NaN v {feature}: {nan_count} hodnôt")
                if feature in technical_indicators:
                    # Pre technické indikátory použijeme rozumné default hodnoty
                    if "RSI" in feature:
                        features_df[feature] = features_df[feature].fillna(50.0)
                    elif "ATR" in feature:
                        features_df[feature] = features_df[feature].fillna(features_df["close"] * 0.01)
                    elif "EMA" in feature or "vwap" in feature:
                        features_df[feature] = features_df[feature].fillna(features_df["close"])
                    elif "bollinger" in feature:
                        if "upper" in feature:
                            features_df[feature] = features_df[feature].fillna(features_df["close"] * 1.02)
                        elif "lower" in feature:
                            features_df[feature] = features_df[feature].fillna(features_df["close"] * 0.98)
                        elif "width" in feature:
                            features_df[feature] = features_df[feature].fillna(features_df["close"] * 0.04)
                        else:  # middle
                            features_df[feature] = features_df[feature].fillna(features_df["close"])
                    elif "ADX" in feature or "DMP" in feature or "DMN" in feature:
                        features_df[feature] = features_df[feature].fillna(25.0)
                    else:
                        features_df[feature] = features_df[feature].fillna(0.0)
                elif feature == "VWAP_pta":
                    features_df[feature] = features_df[feature].fillna(features_df["close"])
                elif feature == "hmm_state_3c_volatility_5m":
                    features_df[feature] = features_df[feature].fillna(1)
                elif feature == "spread":
                    features_df[feature] = features_df[feature].fillna(0.0001)
                elif feature == "mid_price":
                    features_df[feature] = features_df[feature].fillna(features_df["close"])
                else:
                    features_df[feature] = features_df[feature].fillna(0.0)

    # Finálna validácia - žiadne NaN hodnoty nesmú zostať
    final_df = features_df[expected_features]
    total_nan = final_df.isna().sum().sum()
    if total_nan > 0:
        logger.error(f"KRITICKÁ CHYBA: Stále zostáva {total_nan} NaN hodnôt!")
        for col in expected_features:
            nan_count = final_df[col].isna().sum()
            if nan_count > 0:
                logger.error(f"  {col}: {nan_count} NaN hodnôt")
        # Posledná záchrana - nahradíme všetky zostávajúce NaN nulami
        final_df = final_df.fillna(0.0)
        logger.warning("Všetky zostávajúce NaN nahradené nulami ako posledná záchrana")

    logger.info(f"✅ Všetky {len(expected_features)} features sú pripravené bez NaN hodnôt")
    return final_df

def validate_config_for_features(config: dict, expected_features: list) -> dict:
    """
    Validuje a upraví konfiguráciu aby zabezpečila výpočet všetkých potrebných features.

    Args:
        config: Konfiguračný slovník
        expected_features: Zoznam očakávaných features

    Returns:
        Upravená konfigurácia
    """
    logger.info("Validujem konfiguráciu pre všetky potrebné features...")

    # Zabezpečíme, že všetky potrebné indikátory sú povolené
    indicator_settings = config.get("indicatorSettings", {})

    # Technické indikátory - 5m timeframe
    if any("ATR_14" in f for f in expected_features):
        if "atr_5m" not in indicator_settings:
            indicator_settings["atr_5m"] = {"enabled": True, "tf": "5m", "basePeriod": 14}
        else:
            indicator_settings["atr_5m"]["enabled"] = True

    if any("RSI_14" in f for f in expected_features):
        if "rsi_5m" not in indicator_settings:
            indicator_settings["rsi_5m"] = {"enabled": True, "tf": "5m", "basePeriod": 14}
        else:
            indicator_settings["rsi_5m"]["enabled"] = True

    if any("EMA_" in f for f in expected_features):
        if "ema_5m" not in indicator_settings:
            indicator_settings["ema_5m"] = {"enabled": True, "tf": "5m", "baseFastPeriod": 9, "baseSlowPeriod": 21}
        else:
            indicator_settings["ema_5m"]["enabled"] = True

    if any("ADX_14" in f or "DMP_14" in f or "DMN_14" in f for f in expected_features):
        if "adx" not in indicator_settings:
            indicator_settings["adx"] = {"enabled": True, "tf": "5m", "basePeriod": 14}
        else:
            indicator_settings["adx"]["enabled"] = True

    if any("bollinger" in f for f in expected_features):
        if "bollinger_5m" not in indicator_settings:
            indicator_settings["bollinger_5m"] = {"enabled": True, "tf": "5m", "basePeriod": 20, "baseStDev": 2.0}
        else:
            indicator_settings["bollinger_5m"]["enabled"] = True

    if any("VWAP_pta" in f for f in expected_features):
        if "vwap_5m" not in indicator_settings:
            indicator_settings["vwap_5m"] = {"enabled": True, "tf": "5m"}
        else:
            indicator_settings["vwap_5m"]["enabled"] = True

    if any("hmm_state" in f for f in expected_features):
        if "hmm_5m" not in indicator_settings:
            indicator_settings["hmm_5m"] = {"enabled": True, "tf": "5m", "n_components": 3, "window": 200, "data_source": "volatility"}
        else:
            indicator_settings["hmm_5m"]["enabled"] = True

    if any("supertrend" in f for f in expected_features):
        if "supertrend_5m" not in indicator_settings:
            indicator_settings["supertrend_5m"] = {"enabled": True, "tf": "5m", "basePeriod": 10, "multiplier": 3.0}
        else:
            indicator_settings["supertrend_5m"]["enabled"] = True

    # Technické indikátory - 1h timeframe
    if any("ATR_14_1h" in f for f in expected_features):
        if "atr_1h" not in indicator_settings:
            indicator_settings["atr_1h"] = {"enabled": True, "tf": "1h", "basePeriod": 14}
        else:
            indicator_settings["atr_1h"]["enabled"] = True

    if any("RSI_14_1h" in f for f in expected_features):
        if "rsi_1h" not in indicator_settings:
            indicator_settings["rsi_1h"] = {"enabled": True, "tf": "1h", "basePeriod": 14}
        else:
            indicator_settings["rsi_1h"]["enabled"] = True

    if any("EMA_9_1h" in f or "EMA_21_1h" in f for f in expected_features):
        if "ema_1h" not in indicator_settings:
            indicator_settings["ema_1h"] = {"enabled": True, "tf": "1h", "baseFastPeriod": 9, "baseSlowPeriod": 21}
        else:
            indicator_settings["ema_1h"]["enabled"] = True

    if any("ADX_14_1h" in f or "DMP_14_1h" in f or "DMN_14_1h" in f for f in expected_features):
        if "adx_1h" not in indicator_settings:
            indicator_settings["adx_1h"] = {"enabled": True, "tf": "1h", "basePeriod": 14}
        else:
            indicator_settings["adx_1h"]["enabled"] = True

    if any("bollinger" in f and "_1h" in f for f in expected_features):
        if "bollinger_1h" not in indicator_settings:
            indicator_settings["bollinger_1h"] = {"enabled": True, "tf": "1h", "basePeriod": 20, "baseStDev": 2.0}
        else:
            indicator_settings["bollinger_1h"]["enabled"] = True

    if any("VWAP_pta_1h" in f for f in expected_features):
        if "vwap_1h" not in indicator_settings:
            indicator_settings["vwap_1h"] = {"enabled": True, "tf": "1h"}
        else:
            indicator_settings["vwap_1h"]["enabled"] = True

    if any("hmm_state" in f and "_1h" in f for f in expected_features):
        if "hmm_1h" not in indicator_settings:
            indicator_settings["hmm_1h"] = {"enabled": True, "tf": "1h", "n_components": 3, "window": 200, "data_source": "volatility"}
        else:
            indicator_settings["hmm_1h"]["enabled"] = True

    if any("supertrend" in f and "_1h" in f for f in expected_features):
        if "supertrend_1h" not in indicator_settings:
            indicator_settings["supertrend_1h"] = {"enabled": True, "tf": "1h", "basePeriod": 10, "multiplier": 3.0}
        else:
            indicator_settings["supertrend_1h"]["enabled"] = True

    # Order flow features
    if any(f in expected_features for f in ["volume_delta_30s", "volume_delta_2m", "trade_count_delta_100t", "trade_count_delta_500t", "cvd_reset_daily"]):
        if "orderFlowFeatures" not in indicator_settings:
            indicator_settings["orderFlowFeatures"] = {
                "enabled": True,
                "sourceTf": "trades",
                "volumeDeltaPeriods": ["30s", "2m"],
                "tradeCountDeltaPeriods": ["100t", "500t"],
                "cvdEnabled": True,
                "cvdResetFrequency": "daily"
            }
        else:
            indicator_settings["orderFlowFeatures"]["enabled"] = True

    config["indicatorSettings"] = indicator_settings
    logger.info("✅ Konfigurácia validovaná a upravená")
    return config

def add_1h_indicators_to_config(config: dict) -> dict:
    """
    Pridá 1h indikátory do konfigurácie a SuperTrend aj pre 5m timeframe.

    Args:
        config: Konfiguračný slovník

    Returns:
        Upravená konfigurácia s 1h indikátormi a SuperTrend
    """
    logger.info("Pridávam 1h indikátory a SuperTrend do konfigurácie...")

    indicator_settings = config.get("indicatorSettings", {})

    # Pridanie SuperTrend pre 5m timeframe (ak neexistuje)
    if "supertrend_5m" not in indicator_settings:
        indicator_settings["supertrend_5m"] = {"enabled": True, "tf": "5m", "basePeriod": 10, "multiplier": 3.0}
        logger.info("Pridaný 5m indikátor: supertrend_5m")

    # Pridanie 1h indikátorov
    h1_indicators = {
        "atr_1h": {"enabled": True, "tf": "1h", "basePeriod": 14},
        "rsi_1h": {"enabled": True, "tf": "1h", "basePeriod": 14},
        "ema_1h": {"enabled": True, "tf": "1h", "baseFastPeriod": 9, "baseSlowPeriod": 21},
        "adx_1h": {"enabled": True, "tf": "1h", "basePeriod": 14},
        "bollinger_1h": {"enabled": True, "tf": "1h", "basePeriod": 20, "baseStDev": 2.0},
        "vwap_1h": {"enabled": True, "tf": "1h"},
        "hmm_1h": {"enabled": True, "tf": "1h", "n_components": 3, "window": 200, "data_source": "volatility"},
        "supertrend_1h": {"enabled": True, "tf": "1h", "basePeriod": 10, "multiplier": 3.0}
    }

    for name, settings in h1_indicators.items():
        if name not in indicator_settings:
            indicator_settings[name] = settings
            logger.info(f"Pridaný 1h indikátor: {name}")
        else:
            logger.info(f"1h indikátor {name} už existuje v konfigurácii")

    # Pridanie SuperTrend features do feature_columns ak tam nie sú
    env_settings = config.get("envSettings", {})
    feature_columns = env_settings.get("feature_columns", [])

    supertrend_features = [
        "supertrend_10_3.0",
        "supertrend_direction_10_3.0",
        "supertrend_10_3.0_1h",
        "supertrend_direction_10_3.0_1h"
    ]

    added_features = []
    for feature in supertrend_features:
        if feature not in feature_columns:
            feature_columns.append(feature)
            added_features.append(feature)

    if added_features:
        env_settings["feature_columns"] = feature_columns
        config["envSettings"] = env_settings
        logger.info(f"Pridané SuperTrend features: {added_features}")

    config["indicatorSettings"] = indicator_settings
    logger.info("✅ 1h indikátory a SuperTrend pridané do konfigurácie")
    return config

def precompute_features_for_multiple_timeframes(config_path: str, raw_data_dir: str, output_dir: str,
                                              start_date: str, end_date: str,
                                              timeframes: list = None, enable_1h_indicators: bool = False):
    """
    Predpočíta features pre viacero timeframe-ov naraz.

    Args:
        config_path: Cesta k JSON konfiguračnému súboru
        raw_data_dir: Adresár so surovými dátami
        output_dir: Adresár pre výstupné parquet súbory s features
        start_date: Začiatočný dátum (YYYY-MM-DD)
        end_date: Koncový dátum (YYYY-MM-DD)
        timeframes: Zoznam timeframe-ov na spracovanie (napr. ["1s", "5m", "1h"])
        enable_1h_indicators: Ak True, pridá 1h indikátory do konfigurácie
    """
    if timeframes is None:
        timeframes = ["1s", "5m", "1h"]

    logger.info(f"🚀 Spúšťam predpočítanie features pre timeframe-y: {timeframes}")
    logger.info(f"Obdobie: {start_date} - {end_date}")

    success_count = 0
    total_count = len(timeframes)

    for i, tf in enumerate(timeframes, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 Spracovávam timeframe {i}/{total_count}: {tf}")
        logger.info(f"{'='*60}")

        try:
            precompute_features_for_simulation(
                config_path=config_path,
                raw_data_dir=raw_data_dir,
                output_dir=output_dir,
                start_date=start_date,
                end_date=end_date,
                primary_timeframe=tf,
                enable_1h_indicators=enable_1h_indicators
            )
            success_count += 1
            logger.info(f"✅ Timeframe {tf} úspešne dokončený!")

        except Exception as e:
            logger.error(f"❌ Chyba pri spracovaní timeframe {tf}: {e}")
            logger.error(f"Pokračujem s ďalším timeframe-om...")

    logger.info(f"\n{'='*60}")
    logger.info(f"🎉 ZHRNUTIE: {success_count}/{total_count} timeframe-ov úspešne spracovaných")
    logger.info(f"{'='*60}")

    if success_count == total_count:
        logger.info("✅ Všetky timeframe-y boli úspešne spracované!")
    else:
        logger.warning(f"⚠️ {total_count - success_count} timeframe-ov zlyhalo")

def precompute_features_for_simulation(config_path: str, raw_data_dir: str, output_dir: str,
                                     start_date: str, end_date: str, enable_1h_indicators: bool = False,
                                     primary_timeframe: str = None):
    """
    Predpočíta features z konfigurácie a uloží ich do parquet súborov.

    Args:
        config_path: Cesta k JSON konfiguračnému súboru
        raw_data_dir: Adresár so surovými dátami (OHLCV, orderbooks, trades)
        output_dir: Adresár pre výstupné parquet súbory s features
        start_date: Začiatočný dátum (YYYY-MM-DD)
        end_date: Koncový dátum (YYYY-MM-DD)
        enable_1h_indicators: Ak True, pridá 1h indikátory do konfigurácie
        primary_timeframe: Ak je zadané, prepíše primaryTimeframe z konfigurácie
    """

    # Načítanie konfigurácie
    with open(config_path, 'r') as f:
        config = json.load(f)

    # Pridanie 1h indikátorov ak je požadované
    if enable_1h_indicators:
        config = add_1h_indicators_to_config(config)

    symbol = config["symbol"]

    # Použiť zadaný timeframe alebo ten z konfigurácie
    if primary_timeframe is not None:
        primary_tf = primary_timeframe
        config["primaryTimeframe"] = primary_timeframe
        logger.info(f"🔧 Prepísaný primaryTimeframe na: {primary_timeframe}")
    else:
        primary_tf = config["primaryTimeframe"]

    expected_features = config["envSettings"]["feature_columns"]

    logger.info(f"Predpočítavam features pre {symbol}, timeframe {primary_tf}")
    logger.info(f"Obdobie: {start_date} - {end_date}")
    logger.info(f"Očakávaných features: {len(expected_features)}")

    # Validácia a úprava konfigurácie
    config = validate_config_for_features(config, expected_features)

    # Načítanie surových dát - definujeme raw_data_path skôr
    raw_data_path = Path(raw_data_dir)
    data_dict = {}

    start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)

    # Ak nemáme 5m dáta, použijeme primary_tf dáta pre technické indikátory
    if primary_tf != "5m":
        logger.info(f"Upravujem konfiguráciu pre použitie {primary_tf} dát namiesto 5m pre technické indikátory")
        indicator_settings = config.get("indicatorSettings", {})

        # Zmeníme timeframe pre všetky 5m indikátory na primary_tf
        for indicator_name in ["atr_5m", "rsi_5m", "ema_5m", "adx", "bollinger_5m", "vwap_5m", "hmm_5m", "volumeImbalance_5m", "supertrend_5m"]:
            if indicator_name in indicator_settings:
                indicator_settings[indicator_name]["tf"] = primary_tf
                logger.info(f"Zmenený timeframe pre {indicator_name}: 5m -> {primary_tf}")

        config["indicatorSettings"] = indicator_settings

    # Ak nemáme 1h dáta, použijeme primary_tf dáta pre 1h technické indikátory
    # POZOR: Toto sa vykoná iba ak 1h dáta skutočne nie sú dostupné
    if primary_tf != "1h":
        # Najprv skontrolujeme, či 1h dáta existujú
        h1_data_available = False
        h1_ohlcv_dir = raw_data_path / "ohlcv" / "1h"
        if h1_ohlcv_dir.exists():
            h1_files = list(h1_ohlcv_dir.glob("*.parquet"))
            if h1_files:
                # Skontrolujeme, či máme 1h súbory pre požadované obdobie
                for file in h1_files:
                    file_date_str = file.stem
                    try:
                        file_date = datetime.strptime(file_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                        if start_dt <= file_date <= end_dt:
                            h1_data_available = True
                            break
                    except ValueError:
                        continue

        if not h1_data_available:
            logger.warning(f"1h dáta nie sú dostupné pre obdobie {start_date} - {end_date}")
            logger.info(f"Upravujem konfiguráciu pre použitie {primary_tf} dát namiesto 1h pre 1h technické indikátory")
            indicator_settings = config.get("indicatorSettings", {})

            # Zmeníme timeframe pre všetky 1h indikátory na primary_tf iba ak 1h dáta nie sú dostupné
            for indicator_name in ["atr_1h", "rsi_1h", "ema_1h", "adx_1h", "bollinger_1h", "vwap_1h", "hmm_1h", "supertrend_1h"]:
                if indicator_name in indicator_settings:
                    indicator_settings[indicator_name]["tf"] = primary_tf
                    logger.info(f"Zmenený timeframe pre {indicator_name}: 1h -> {primary_tf}")

            config["indicatorSettings"] = indicator_settings
        else:
            logger.info(f"✅ 1h dáta sú dostupné - 1h indikátory budú používať skutočné 1h dáta")

    def load_ohlcv_timeframe(timeframe: str) -> bool:
        """Helper function to load OHLCV data for a specific timeframe"""
        ohlcv_dir = raw_data_path / "ohlcv" / timeframe
        if not ohlcv_dir.exists():
            logger.warning(f"OHLCV adresár neexistuje: {ohlcv_dir}")
            return False

        logger.info(f"Načítavam OHLCV dáta z {ohlcv_dir}")
        ohlcv_files = sorted(ohlcv_dir.glob("*.parquet"))

        # Filtrovanie súborov podľa dátumu
        relevant_files = []
        for file in ohlcv_files:
            file_date_str = file.stem  # napr. "2025-06-24"
            try:
                file_date = datetime.strptime(file_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                if start_dt <= file_date <= end_dt:
                    relevant_files.append(file)
            except ValueError:
                continue

        if relevant_files:
            ohlcv_dfs = []
            for file in relevant_files:
                df = pd.read_parquet(file)
                if "timestamp" in df.columns:
                    df.set_index("timestamp", inplace=True)
                df.index = pd.to_datetime(df.index, utc=True)

                # Aplikácia data quality fix
                df = check_and_fix_data_quality(df, str(file))

                ohlcv_dfs.append(df)

            data_dict[timeframe] = pd.concat(ohlcv_dfs).sort_index()
            logger.info(f"Načítaných {len(data_dict[timeframe])} OHLCV riadkov pre {timeframe}")
            return True
        else:
            logger.warning(f"Žiadne OHLCV súbory pre obdobie {start_date} - {end_date} v {timeframe}")
            return False

    # 1. Načítanie OHLCV dát (primary timeframe)
    if not load_ohlcv_timeframe(primary_tf):
        logger.error(f"Nepodarilo sa načítať primárny timeframe {primary_tf}")
        return

    # 2. Načítanie OHLCV dát pre 5m (ak nie je primárny)
    if primary_tf != "5m":
        load_ohlcv_timeframe("5m")

    # 3. Načítanie OHLCV dát pre 1h (ak nie je primárny)
    if primary_tf != "1h":
        load_ohlcv_timeframe("1h")

    # 4. Načítanie orderbook dát (ak existujú)
    orderbook_dir = raw_data_path / "orderbooks"
    if orderbook_dir.exists():
        logger.info(f"Načítavam orderbook dáta z {orderbook_dir}")
        try:
            orderbook_files = sorted(orderbook_dir.glob("*.parquet"))
            relevant_ob_files = []
            for file in orderbook_files:
                file_date_str = file.stem
                try:
                    file_date = datetime.strptime(file_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                    if start_dt <= file_date <= end_dt:
                        relevant_ob_files.append(file)
                except ValueError:
                    continue

            if relevant_ob_files:
                ob_dfs = []
                for file in relevant_ob_files:
                    df = pd.read_parquet(file)
                    if "timestamp" in df.columns:
                        df.set_index("timestamp", inplace=True)
                    df.index = pd.to_datetime(df.index, utc=True)
                    ob_dfs.append(df)

                data_dict["orderbooks"] = pd.concat(ob_dfs).sort_index()
                logger.info(f"Načítaných {len(data_dict['orderbooks'])} orderbook riadkov")
            else:
                logger.info("Žiadne orderbook súbory pre dané obdobie")
        except Exception as e:
            logger.warning(f"Chyba pri načítaní orderbook dát: {e}")

    # 5. Načítanie trade dát (ak existujú)
    trades_dir = raw_data_path / "trades"
    if trades_dir.exists():
        logger.info(f"Načítavam trade dáta z {trades_dir}")
        try:
            trade_files = sorted(trades_dir.glob("*.parquet"))
            relevant_trade_files = []
            for file in trade_files:
                file_date_str = file.stem
                try:
                    file_date = datetime.strptime(file_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                    if start_dt <= file_date <= end_dt:
                        relevant_trade_files.append(file)
                except ValueError:
                    continue

            if relevant_trade_files:
                trade_dfs = []
                for file in relevant_trade_files:
                    df = pd.read_parquet(file)
                    if "timestamp" in df.columns:
                        df.set_index("timestamp", inplace=True)
                    df.index = pd.to_datetime(df.index, utc=True)
                    trade_dfs.append(df)

                data_dict["trades"] = pd.concat(trade_dfs).sort_index()
                logger.info(f"Načítaných {len(data_dict['trades'])} trade riadkov")
            else:
                logger.info("Žiadne trade súbory pre dané obdobie")
        except Exception as e:
            logger.warning(f"Chyba pri načítaní trade dát: {e}")

    # 6. Výpočet features
    logger.info("Začínam výpočet features...")
    try:
        features_df, feature_names = calculate_and_merge_indicators(
            data=data_dict,
            cfg=config,
            skip_hmm=False
        )

        logger.info(f"Vypočítaných {len(feature_names)} features: {feature_names[:10]}...")
        logger.info(f"Features DataFrame shape: {features_df.shape}")

        # Doplnenie chýbajúcich features na požadovaných 67
        expected_features = config["envSettings"]["feature_columns"]
        logger.info(f"Doplňujem chýbajúce features na celkovo {len(expected_features)} features...")

        features_df = fill_missing_features(features_df, expected_features)
        logger.info(f"Finálny features DataFrame shape: {features_df.shape}")

    except Exception as e:
        logger.error(f"Chyba pri výpočte features: {e}")
        return

    # 7. Uloženie do parquet súborov
    output_path = Path(output_dir)
    output_symbol_dir = output_path / symbol / primary_tf
    output_symbol_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Ukladám features do {output_symbol_dir}")
    
    # Rozdelenie podľa dní a uloženie
    features_df_grouped = features_df.groupby(features_df.index.date)
    
    for date, day_df in features_df_grouped:
        output_file = output_symbol_dir / f"{date}.parquet"
        # Reset index to make timestamp a column (required by simulate_trading.py)
        day_df_with_timestamp = day_df.reset_index()
        day_df_with_timestamp.to_parquet(output_file, engine='pyarrow', index=False)
        logger.info(f"Uložený súbor {output_file} s {len(day_df_with_timestamp)} riadkami")

    logger.info("✅ Predpočítanie features dokončené!")

    # 8. Detailná validácia výsledkov
    logger.info("Validujem výsledky...")
    expected_features = config["envSettings"]["feature_columns"]

    # Kontrola všetkých očakávaných features
    missing_features = [f for f in expected_features if f not in features_df.columns]
    if missing_features:
        logger.error(f"CHÝBAJÚCE FEATURES: {missing_features}")
    else:
        logger.info("✅ Všetky potrebné features sú prítomné")

    # Kontrola NaN hodnôt
    total_nan = features_df[expected_features].isna().sum().sum()
    if total_nan > 0:
        logger.error(f"ZOSTÁVAJÚCE NaN HODNOTY: {total_nan}")
        for col in expected_features:
            nan_count = features_df[col].isna().sum()
            if nan_count > 0:
                logger.error(f"  {col}: {nan_count} NaN hodnôt")
    else:
        logger.info("✅ Žiadne NaN hodnoty")

    # Kontrola nekonečných hodnôt
    inf_count = np.isinf(features_df[expected_features]).sum().sum()
    if inf_count > 0:
        logger.warning(f"NEKONEČNÉ HODNOTY: {inf_count}")
        features_df = features_df.replace([np.inf, -np.inf], np.nan).fillna(0.0)
        logger.info("Nekonečné hodnoty nahradené nulami")

    # Štatistiky features
    logger.info(f"Finálny DataFrame: {features_df.shape}")
    logger.info(f"Časové rozpätie: {features_df.index.min()} - {features_df.index.max()}")
    logger.info(f"Počet features: {len(expected_features)}")

    # Ukážka hodnôt pre kontrolu
    logger.info("Ukážka posledných hodnôt kľúčových features:")
    key_features = ["close", "volume", "ATR_14", "RSI_14", "EMA_9", "VWAP_pta",
                   "supertrend_10_3.0", "supertrend_direction_10_3.0",
                   "ATR_14_1h", "RSI_14_1h", "EMA_9_1h", "vwap_1h",
                   "supertrend_10_3.0_1h", "supertrend_direction_10_3.0_1h"]
    for feature in key_features:
        if feature in features_df.columns:
            last_val = features_df[feature].iloc[-1]
            if "direction" in feature:
                logger.info(f"  {feature}: {last_val:.0f} ({'Bullish' if last_val > 0 else 'Bearish'})")
            else:
                logger.info(f"  {feature}: {last_val:.6f}")

    extra_features = [f for f in features_df.columns if f not in expected_features]
    if extra_features:
        logger.info(f"Extra features (nepoužijú sa): {extra_features[:5]}...")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Predpočíta features pre simuláciu tradingu")
    parser.add_argument("--config", required=True, help="Cesta k JSON config súboru")
    parser.add_argument("--raw-data-dir", required=True, help="Adresár so surovými dátami")
    parser.add_argument("--output-dir", required=True, help="Výstupný adresár pre features")
    parser.add_argument("--start", required=True, help="Začiatočný dátum (YYYY-MM-DD)")
    parser.add_argument("--end", required=True, help="Koncový dátum (YYYY-MM-DD)")
    parser.add_argument("--enable-1h-indicators", action="store_true",
                       help="Pridá 1h indikátory (ATR, RSI, EMA, ADX, Bollinger, VWAP, HMM) do konfigurácie")
    parser.add_argument("--timeframes", nargs="+", default=None,
                       help="Zoznam timeframe-ov na spracovanie (napr. --timeframes 1s 5m 1h)")
    parser.add_argument("--timeframe", type=str, default=None,
                       help="Jeden timeframe na spracovanie (prepíše primaryTimeframe z config)")

    args = parser.parse_args()

    # Ak sú zadané viacero timeframe-ov, použiť multi-timeframe funkciu
    if args.timeframes:
        precompute_features_for_multiple_timeframes(
            config_path=args.config,
            raw_data_dir=args.raw_data_dir,
            output_dir=args.output_dir,
            start_date=args.start,
            end_date=args.end,
            timeframes=args.timeframes,
            enable_1h_indicators=args.enable_1h_indicators
        )
    else:
        # Použiť single-timeframe funkciu
        precompute_features_for_simulation(
            config_path=args.config,
            raw_data_dir=args.raw_data_dir,
            output_dir=args.output_dir,
            start_date=args.start,
            end_date=args.end,
            enable_1h_indicators=args.enable_1h_indicators,
            primary_timeframe=args.timeframe
        )