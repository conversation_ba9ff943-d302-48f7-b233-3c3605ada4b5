# 1-hodinové indikátory v precompute_features.py

## Prehľad

Bol pridaný support pre 1-hodinové (1h) technické indikátory do `precompute_features.py` skriptu. Tieto indikátory poskytujú dlhodobe<PERSON><PERSON><PERSON> kontext pre trading rozhodnutia a pomáhajú identifikovať hlavné trendy na vyšších timeframe-och.

## Podporované 1h indikátory

| Indikátor | Názov v features | Popis |
|-----------|------------------|-------|
| ATR | `ATR_14_1h` | Average True Range (14 period) |
| RSI | `RSI_14_1h` | Relative Strength Index (14 period) |
| EMA Fast | `EMA_9_1h` | Exponential Moving Average (9 period) |
| EMA Slow | `EMA_21_1h` | Exponential Moving Average (21 period) |
| ADX | `ADX_14_1h` | Average Directional Index (14 period) |
| DM+ | `DMP_14_1h` | Directional Movement Plus (14 period) |
| DM- | `DMN_14_1h` | Directional Movement Minus (14 period) |
| Bollinger Upper | `bollinger_bands_upper_20_2.0_1h` | Bollinger Bands Upper (20, 2.0) |
| Bollinger Middle | `bollinger_bands_middle_20_2.0_1h` | Bollinger Bands Middle (20, 2.0) |
| Bollinger Lower | `bollinger_bands_lower_20_2.0_1h` | Bollinger Bands Lower (20, 2.0) |
| Bollinger Width | `bollinger_bands_width_20_2.0_1h` | Bollinger Bands Width (20, 2.0) |
| VWAP | `vwap_1h` | Volume Weighted Average Price |
| HMM State | `hmm_state_3c_volatility_1h` | Hidden Markov Model State |

## Použitie

### 1. Automatické pridanie 1h indikátorov

```bash
python3 precompute_features.py \
  --config temp_config_1s.json \
  --raw-data-dir raw_data \
  --output-dir parquet_processed \
  --start 2024-04-01 \
  --end 2024-04-30 \
  --enable-1h-indicators
```

### 2. Manuálne pridanie do konfigurácie

Pridajte do `indicatorSettings` sekcie vášho config súboru:

```json
{
  "indicatorSettings": {
    "atr_1h": {"enabled": true, "tf": "1h", "basePeriod": 14},
    "rsi_1h": {"enabled": true, "tf": "1h", "basePeriod": 14},
    "ema_1h": {"enabled": true, "tf": "1h", "baseFastPeriod": 9, "baseSlowPeriod": 21},
    "adx_1h": {"enabled": true, "tf": "1h", "basePeriod": 14},
    "bollinger_1h": {"enabled": true, "tf": "1h", "basePeriod": 20, "baseStDev": 2.0},
    "vwap_1h": {"enabled": true, "tf": "1h"},
    "hmm_1h": {"enabled": true, "tf": "1h", "n_components": 3, "window": 200, "data_source": "volatility"}
  }
}
```

A pridajte features do `envSettings.feature_columns`:

```json
{
  "envSettings": {
    "feature_columns": [
      "ATR_14_1h", "RSI_14_1h", "EMA_9_1h", "EMA_21_1h",
      "ADX_14_1h", "DMP_14_1h", "DMN_14_1h",
      "bollinger_bands_upper_20_2.0_1h",
      "bollinger_bands_middle_20_2.0_1h",
      "bollinger_bands_lower_20_2.0_1h",
      "bollinger_bands_width_20_2.0_1h",
      "vwap_1h", "hmm_state_3c_volatility_1h"
    ]
  }
}
```

## Požiadavky na dáta

Pre správne fungovanie 1h indikátorov potrebujete:

1. **1h OHLCV dáta** v adresári `raw_data/ohlcv/1h/`
2. Súbory vo formáte: `YYYY-MM-DD.parquet`
3. Stĺpce: `timestamp`, `open`, `high`, `low`, `close`, `volume`

### Štruktúra adresárov

```
raw_data/
├── ohlcv/
│   ├── 1s/          # 1-sekundové dáta
│   ├── 5m/          # 5-minútové dáta  
│   └── 1h/          # 1-hodinové dáta (nové!)
├── orderbooks/      # Orderbook dáta
└── trades/          # Trade dáta
```

## Príklady

### 1. Interaktívny test

```bash
python3 example_1h_indicators.py
```

### 2. Automatizovaný test

```bash
python3 test_1h_indicators.py
```

### 3. Použitie s existujúcim config súborom

```bash
# Bez 1h indikátorov (pôvodné správanie)
python3 precompute_features.py --config config.json --raw-data-dir raw_data --output-dir output --start 2024-04-01 --end 2024-04-30

# S 1h indikátormi
python3 precompute_features.py --config config.json --raw-data-dir raw_data --output-dir output --start 2024-04-01 --end 2024-04-30 --enable-1h-indicators
```

## Výhody 1h indikátorov

### 1. Trend identifikácia
- **EMA_9_1h vs EMA_21_1h**: Identifikácia hlavného trendu
- **ADX_14_1h**: Sila trendu na vyššom timeframe-e

### 2. Volatilita analýza
- **ATR_14_1h**: Dlhodobá volatilita pre risk management
- **Bollinger Bands 1h**: Identifikácia prekúpených/prepredaných úrovní

### 3. Momentum analýza
- **RSI_14_1h**: Dlhodobé momentum pre filtrovanie signálov

### 4. Volume analýza
- **VWAP_1h**: Referenčná cena pre inštitucionálnych traderov

### 5. Market regime detection
- **HMM_1h**: Identifikácia market režimov (trending, ranging, volatile)

## Trading stratégie s 1h indikátormi

### 1. Trend filter
```python
# Vstup do LONG pozície iba ak 1h trend je bullish
if EMA_9_1h > EMA_21_1h and ADX_14_1h > 25:
    # Povoliť LONG signály z agenta
    allow_long = True
```

### 2. Volatility filter
```python
# Obchodovať iba počas vysokej volatility
if ATR_14_1h > ATR_14_1h.rolling(20).mean():
    # Vysoká volatilita - aktívne obchodovanie
    active_trading = True
```

### 3. Mean reversion
```python
# Mean reversion na 1h Bollinger Bands
if close < bollinger_lower_1h:
    # Potenciálny LONG setup
    mean_reversion_long = True
```

## Troubleshooting

### Chyba: "1h OHLCV adresár neexistuje"
```bash
# Vytvorte adresár a stiahnite 1h dáta
mkdir -p raw_data/ohlcv/1h
# Použite coinapi.py alebo binance_to_msgpack.py pre stiahnutie 1h dát
```

### Chyba: "Žiadne 1h OHLCV súbory"
```bash
# Skontrolujte formát súborov
ls raw_data/ohlcv/1h/
# Očakávané: 2024-04-01.parquet, 2024-04-02.parquet, ...
```

### Chyba: "1h indikátory majú všetky NaN hodnoty"
- Skontrolujte, že 1h dáta obsahujú platné OHLCV hodnoty
- Overte, že timestamp index je správne nastavený

## Súbory

- `precompute_features.py` - Hlavný skript (upravený)
- `example_1h_indicators.py` - Interaktívny príklad
- `test_1h_indicators.py` - Automatizované testy
- `config_with_1h_indicators_example.json` - Príklad konfigurácie
- `CHANGELOG_1h_indicators.md` - Detailný changelog
- `README_1h_indicators.md` - Tento súbor

## Kompatibilita

- ✅ Spätne kompatibilné s existujúcimi config súbormi
- ✅ Funguje s všetkými primárnymi timeframe-mi (1s, 5m, 1h)
- ✅ Automatický fallback ak 1h dáta nie sú dostupné
- ✅ Integrácia s existujúcimi trading skriptami
