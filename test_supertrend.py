#!/usr/bin/env python3
"""
Test skript pre overenie funkčnosti SuperTrend indikátora v precompute_features.py
"""

import pandas as pd
import pandas_ta as ta
import numpy as np
from pathlib import Path
import json
import tempfile
from precompute_features import add_1h_indicators_to_config, validate_config_for_features

def test_supertrend_calculation():
    """Test základného výpočtu SuperTrend indikátora"""
    print("🧪 Test: Základný výpočet SuperTrend indikátora")
    
    # Vytvorenie testovacích OHLC dát
    np.random.seed(42)
    dates = pd.date_range('2025-01-01', periods=100, freq='5T')
    
    # Simulácia realistických OHLC dát
    base_price = 2.0
    price_changes = np.random.normal(0, 0.01, 100).cumsum()
    close_prices = base_price + price_changes
    
    # OHLC s realistickými spread-mi
    high_prices = close_prices + np.random.uniform(0.001, 0.005, 100)
    low_prices = close_prices - np.random.uniform(0.001, 0.005, 100)
    open_prices = np.roll(close_prices, 1)
    open_prices[0] = close_prices[0]
    
    test_data = pd.DataFrame({
        'timestamp': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': np.random.uniform(1000, 10000, 100)
    }).set_index('timestamp')
    
    print(f"  Vytvorené testové dáta: {len(test_data)} riadkov")
    print(f"  Close price range: {test_data['close'].min():.6f} - {test_data['close'].max():.6f}")
    
    # Výpočet SuperTrend
    period = 10
    multiplier = 3.0
    
    st = ta.supertrend(test_data['high'], test_data['low'], test_data['close'], 
                      length=period, multiplier=multiplier)
    
    if st is not None and not st.empty:
        print(f"  ✅ SuperTrend vypočítaný úspešne")
        print(f"  Stĺpce: {list(st.columns)}")
        
        # Kontrola očakávaných stĺpcov
        expected_cols = [f"SUPERT_{period}_{multiplier}", f"SUPERTd_{period}_{multiplier}"]
        for col in expected_cols:
            if col in st.columns:
                valid_count = st[col].dropna().count()
                print(f"  ✅ {col}: {valid_count} platných hodnôt")
                if valid_count > 0:
                    print(f"     Posledná hodnota: {st[col].dropna().iloc[-1]:.6f}")
            else:
                print(f"  ❌ {col}: chýba v výsledku")
        
        return True
    else:
        print("  ❌ SuperTrend výpočet zlyhal")
        return False

def test_supertrend_config():
    """Test pridania SuperTrend do konfigurácie"""
    print("\n🧪 Test: Pridanie SuperTrend do konfigurácie")
    
    # Základná konfigurácia
    config = {
        "symbol": "XRPUSDC",
        "primaryTimeframe": "5m",
        "indicatorSettings": {},
        "envSettings": {
            "feature_columns": [
                "close", "volume", "ATR_14", "RSI_14",
                "supertrend_10_3.0", "supertrend_direction_10_3.0",
                "supertrend_10_3.0_1h", "supertrend_direction_10_3.0_1h"
            ]
        }
    }
    
    print(f"  Očakávané SuperTrend features: {[f for f in config['envSettings']['feature_columns'] if 'supertrend' in f]}")
    
    # Pridanie 1h indikátorov (vrátane SuperTrend)
    config_with_1h = add_1h_indicators_to_config(config.copy())
    
    # Validácia konfigurácie
    expected_features = config["envSettings"]["feature_columns"]
    validated_config = validate_config_for_features(config_with_1h, expected_features)
    
    # Kontrola SuperTrend indikátorov
    indicators = validated_config['indicatorSettings']
    
    supertrend_indicators = [key for key in indicators.keys() if 'supertrend' in key]
    print(f"  Vytvorené SuperTrend indikátory: {supertrend_indicators}")
    
    # Kontrola 5m SuperTrend
    if "supertrend_5m" in indicators:
        st_5m = indicators["supertrend_5m"]
        print(f"  ✅ supertrend_5m: tf={st_5m.get('tf')}, period={st_5m.get('basePeriod')}, mult={st_5m.get('multiplier')}")
    else:
        print("  ❌ supertrend_5m chýba")
    
    # Kontrola 1h SuperTrend
    if "supertrend_1h" in indicators:
        st_1h = indicators["supertrend_1h"]
        print(f"  ✅ supertrend_1h: tf={st_1h.get('tf')}, period={st_1h.get('basePeriod')}, mult={st_1h.get('multiplier')}")
    else:
        print("  ❌ supertrend_1h chýba")
    
    return len(supertrend_indicators) >= 2

def test_supertrend_features():
    """Test správneho pomenovania SuperTrend features"""
    print("\n🧪 Test: Správne pomenovanie SuperTrend features")
    
    expected_features = [
        "supertrend_10_3.0",           # SuperTrend hodnota 5m
        "supertrend_direction_10_3.0", # SuperTrend smer 5m
        "supertrend_10_3.0_1h",        # SuperTrend hodnota 1h
        "supertrend_direction_10_3.0_1h" # SuperTrend smer 1h
    ]
    
    print(f"  Očakávané SuperTrend features: {expected_features}")
    
    # Simulácia features DataFrame
    test_df = pd.DataFrame({
        'close': [2.0, 2.1, 2.05, 1.95, 2.02],
        'supertrend_10_3.0': [1.98, 2.08, 2.03, 1.93, 2.00],
        'supertrend_direction_10_3.0': [1, 1, 1, -1, 1],
        'supertrend_10_3.0_1h': [1.97, 2.07, 2.02, 1.92, 1.99],
        'supertrend_direction_10_3.0_1h': [1, 1, 1, -1, 1]
    })
    
    # Kontrola, že všetky features existujú
    missing_features = [f for f in expected_features if f not in test_df.columns]
    if not missing_features:
        print("  ✅ Všetky SuperTrend features sú prítomne")
        
        # Kontrola hodnôt
        for feature in expected_features:
            values = test_df[feature]
            if "direction" in feature:
                unique_vals = values.unique()
                print(f"  ✅ {feature}: hodnoty {unique_vals} (očakávané: 1=bullish, -1=bearish)")
            else:
                print(f"  ✅ {feature}: rozsah {values.min():.3f} - {values.max():.3f}")
        
        return True
    else:
        print(f"  ❌ Chýbajúce features: {missing_features}")
        return False

def test_supertrend_interpretation():
    """Test interpretácie SuperTrend signálov"""
    print("\n🧪 Test: Interpretácia SuperTrend signálov")
    
    # Simulácia SuperTrend dát
    test_scenarios = [
        {"close": 2.00, "supertrend": 1.95, "direction": 1, "interpretation": "Bullish trend - cena nad SuperTrend"},
        {"close": 1.90, "supertrend": 1.95, "direction": -1, "interpretation": "Bearish trend - cena pod SuperTrend"},
        {"close": 2.05, "supertrend": 2.00, "direction": 1, "interpretation": "Bullish trend - cena nad SuperTrend"},
        {"close": 1.85, "supertrend": 1.90, "direction": -1, "interpretation": "Bearish trend - cena pod SuperTrend"}
    ]
    
    print("  SuperTrend scenáre:")
    for i, scenario in enumerate(test_scenarios, 1):
        close = scenario["close"]
        st_value = scenario["supertrend"]
        direction = scenario["direction"]
        interpretation = scenario["interpretation"]
        
        # Validácia logiky
        expected_direction = 1 if close > st_value else -1
        is_correct = direction == expected_direction
        
        status = "✅" if is_correct else "❌"
        print(f"  {status} Scenár {i}: Close={close:.2f}, ST={st_value:.2f}, Dir={direction} - {interpretation}")
        
        if not is_correct:
            print(f"      Očakávaný smer: {expected_direction}")
    
    return True

def main():
    """Spustí všetky SuperTrend testy"""
    print("🚀 Spúšťam testy pre SuperTrend indikátor")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    try:
        if test_supertrend_calculation():
            tests_passed += 1
        
        if test_supertrend_config():
            tests_passed += 1
            
        if test_supertrend_features():
            tests_passed += 1
            
        if test_supertrend_interpretation():
            tests_passed += 1
        
        print(f"\n{'='*60}")
        print(f"🎉 Výsledok: {tests_passed}/{total_tests} testov prešlo úspešne!")
        
        if tests_passed == total_tests:
            print("✅ SuperTrend indikátor je pripravený na použitie!")
            print("\n💡 Pre použitie v praxi:")
            print("   python3 precompute_features.py --config <config> --raw-data-dir <dir> \\")
            print("     --output-dir <dir> --start <date> --end <date> --enable-1h-indicators")
            print("\n📊 SuperTrend features:")
            print("   • supertrend_10_3.0 - SuperTrend hodnota (5m)")
            print("   • supertrend_direction_10_3.0 - SuperTrend smer (5m): 1=bullish, -1=bearish")
            print("   • supertrend_10_3.0_1h - SuperTrend hodnota (1h)")
            print("   • supertrend_direction_10_3.0_1h - SuperTrend smer (1h): 1=bullish, -1=bearish")
        else:
            print("❌ Niektoré testy zlyhali - skontrolujte implementáciu")
        
    except Exception as e:
        print(f"❌ Chyba pri testovaní: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
