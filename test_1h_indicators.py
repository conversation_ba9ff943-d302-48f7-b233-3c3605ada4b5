#!/usr/bin/env python3
"""
Test skript pre overenie funkčnosti 1h indikátorov v precompute_features.py
"""

import json
import tempfile
from pathlib import Path
from precompute_features import add_1h_indicators_to_config, validate_config_for_features

def test_add_1h_indicators():
    """Test pridania 1h indikátorov do konfigurácie"""
    print("🧪 Test: Pridanie 1h indikátorov do konfigurácie")
    
    # Základná konfigurácia
    config = {
        "symbol": "XRPUSDC",
        "primaryTimeframe": "1s",
        "indicatorSettings": {
            "atr_5m": {"enabled": True, "tf": "5m", "basePeriod": 14},
            "rsi_5m": {"enabled": True, "tf": "5m", "basePeriod": 14}
        }
    }
    
    print(f"  Pôvodné indikátory: {list(config['indicatorSettings'].keys())}")
    
    # Pridanie 1h indikátorov
    updated_config = add_1h_indicators_to_config(config)
    
    print(f"  Po pridaní 1h: {list(updated_config['indicatorSettings'].keys())}")
    
    # Kontrola, že boli pridané 1h indikátory
    expected_1h = ["atr_1h", "rsi_1h", "ema_1h", "adx_1h", "bollinger_1h", "vwap_1h", "hmm_1h"]
    
    for indicator in expected_1h:
        if indicator in updated_config['indicatorSettings']:
            print(f"  ✅ {indicator} - pridaný")
            # Kontrola timeframe-u
            tf = updated_config['indicatorSettings'][indicator].get('tf')
            if tf == '1h':
                print(f"     ✅ timeframe: {tf}")
            else:
                print(f"     ❌ nesprávny timeframe: {tf}")
        else:
            print(f"  ❌ {indicator} - chýba")
    
    print()
    return updated_config

def test_validate_config():
    """Test validácie konfigurácie s 1h features"""
    print("🧪 Test: Validácia konfigurácie s 1h features")
    
    # Konfigurácia s očakávanými 1h features
    config = {
        "symbol": "XRPUSDC",
        "primaryTimeframe": "1s",
        "indicatorSettings": {},
        "envSettings": {
            "feature_columns": [
                "close", "volume", "ATR_14", "RSI_14",
                "ATR_14_1h", "RSI_14_1h", "EMA_9_1h", "EMA_21_1h"
            ]
        }
    }
    
    expected_features = config["envSettings"]["feature_columns"]
    print(f"  Očakávané features: {expected_features}")
    
    # Validácia
    validated_config = validate_config_for_features(config, expected_features)
    
    # Kontrola, že boli vytvorené potrebné indikátory
    indicators = validated_config['indicatorSettings']
    print(f"  Vytvorené indikátory: {list(indicators.keys())}")
    
    # Kontrola 1h indikátorov
    if "atr_1h" in indicators and indicators["atr_1h"]["tf"] == "1h":
        print("  ✅ ATR_1h indikátor vytvorený správne")
    else:
        print("  ❌ ATR_1h indikátor chýba alebo má nesprávny timeframe")
    
    if "rsi_1h" in indicators and indicators["rsi_1h"]["tf"] == "1h":
        print("  ✅ RSI_1h indikátor vytvorený správne")
    else:
        print("  ❌ RSI_1h indikátor chýba alebo má nesprávny timeframe")
    
    print()
    return validated_config

def test_config_file_creation():
    """Test vytvorenia config súboru s 1h indikátormi"""
    print("🧪 Test: Vytvorenie config súboru s 1h indikátormi")
    
    # Základný config
    base_config = {
        "symbol": "XRPUSDC",
        "primaryTimeframe": "1s",
        "featureParquetDir": "parquet_processed",
        "indicatorSettings": {
            "atr_5m": {"enabled": True, "tf": "5m", "basePeriod": 14},
            "rsi_5m": {"enabled": True, "tf": "5m", "basePeriod": 14}
        },
        "envSettings": {
            "feature_columns": [
                "close", "volume", "ATR_14", "RSI_14",
                "ATR_14_1h", "RSI_14_1h", "EMA_9_1h"
            ]
        }
    }
    
    # Pridanie 1h indikátorov
    config_with_1h = add_1h_indicators_to_config(base_config.copy())
    
    # Uloženie do dočasného súboru
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(config_with_1h, f, indent=2)
        temp_path = f.name
    
    print(f"  Config súbor vytvorený: {temp_path}")
    
    # Načítanie a overenie
    with open(temp_path, 'r') as f:
        loaded_config = json.load(f)
    
    # Kontrola 1h indikátorov
    h1_count = sum(1 for key in loaded_config['indicatorSettings'].keys() if '1h' in key)
    print(f"  Počet 1h indikátorov v súbore: {h1_count}")
    
    if h1_count >= 7:  # Očakávame aspoň 7 1h indikátorov
        print("  ✅ Config súbor obsahuje 1h indikátory")
    else:
        print("  ❌ Config súbor neobsahuje dostatok 1h indikátorov")
    
    # Vyčistenie
    Path(temp_path).unlink()
    print(f"  Dočasný súbor vymazaný: {temp_path}")
    print()

def test_indicator_naming():
    """Test správneho pomenovania 1h indikátorov"""
    print("🧪 Test: Správne pomenovanie 1h indikátorov")
    
    config = {"indicatorSettings": {}}
    config_with_1h = add_1h_indicators_to_config(config)
    
    expected_naming = {
        "atr_1h": {"tf": "1h", "basePeriod": 14},
        "rsi_1h": {"tf": "1h", "basePeriod": 14},
        "ema_1h": {"tf": "1h", "baseFastPeriod": 9, "baseSlowPeriod": 21},
        "adx_1h": {"tf": "1h", "basePeriod": 14},
        "bollinger_1h": {"tf": "1h", "basePeriod": 20, "baseStDev": 2.0},
        "vwap_1h": {"tf": "1h"},
        "hmm_1h": {"tf": "1h", "n_components": 3, "window": 200, "data_source": "volatility"}
    }
    
    for name, expected_settings in expected_naming.items():
        if name in config_with_1h['indicatorSettings']:
            actual_settings = config_with_1h['indicatorSettings'][name]
            
            # Kontrola timeframe-u
            if actual_settings.get('tf') == '1h':
                print(f"  ✅ {name} - správny timeframe")
            else:
                print(f"  ❌ {name} - nesprávny timeframe: {actual_settings.get('tf')}")
            
            # Kontrola ďalších parametrov
            for key, expected_value in expected_settings.items():
                if key != 'tf' and actual_settings.get(key) == expected_value:
                    print(f"     ✅ {key}: {expected_value}")
                elif key != 'tf':
                    print(f"     ❌ {key}: očakávané {expected_value}, skutočné {actual_settings.get(key)}")
        else:
            print(f"  ❌ {name} - chýba v konfigurácii")
    
    print()

def main():
    """Spustí všetky testy"""
    print("🚀 Spúšťam testy pre 1h indikátory")
    print("=" * 60)
    print()
    
    try:
        test_add_1h_indicators()
        test_validate_config()
        test_config_file_creation()
        test_indicator_naming()
        
        print("🎉 Všetky testy dokončené!")
        print()
        print("💡 Pre použitie v praxi:")
        print("   python precompute_features.py --config <config> --raw-data-dir <dir> \\")
        print("     --output-dir <dir> --start <date> --end <date> --enable-1h-indicators")
        
    except Exception as e:
        print(f"❌ Chyba pri testovaní: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
