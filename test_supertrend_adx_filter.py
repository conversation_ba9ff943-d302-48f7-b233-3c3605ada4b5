#!/usr/bin/env python3
"""
Test skript pre overenie funkčnosti SuperTrend + ADX trend filter v simulate_trading_new.py
"""

import pandas as pd
import numpy as np
import json
import sys
import os

# Add current directory to path to import simulate_trading_new
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from simulate_trading_new import evaluate_supertrend_adx_filter
import logging

# Setup basic logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

def test_supertrend_adx_filter_basic():
    """Test základnej funkčnosti SuperTrend + ADX filter"""
    print("🧪 Test: Základná funkčnosť SuperTrend + ADX filter")
    
    # Test konfigurácia
    trend_filter_config = {
        "enabled": True,
        "method": "supertrend_adx",
        "timeframe": "1h",
        "params": {
            "supertrend": {
                "atrPeriod": 10,
                "multiplier": 3.0
            },
            "adx": {
                "period": 14,
                "minAdx": 25
            }
        }
    }
    
    # Test scenáre
    test_scenarios = [
        {
            "name": "Bullish trend - strong ADX",
            "data": {
                "close": 2.00,
                "supertrend_10_3.0_1h": 1.95,
                "supertrend_direction_10_3.0_1h": 1,  # Bullish
                "ADX_14_1h": 30  # Strong trend
            },
            "expected": {
                "allow_long": True,
                "allow_short": False,
                "trend_direction": "bullish"
            }
        },
        {
            "name": "Bearish trend - strong ADX",
            "data": {
                "close": 1.90,
                "supertrend_10_3.0_1h": 1.95,
                "supertrend_direction_10_3.0_1h": -1,  # Bearish
                "ADX_14_1h": 35  # Strong trend
            },
            "expected": {
                "allow_long": False,
                "allow_short": True,
                "trend_direction": "bearish"
            }
        },
        {
            "name": "Bullish trend - weak ADX",
            "data": {
                "close": 2.00,
                "supertrend_10_3.0_1h": 1.95,
                "supertrend_direction_10_3.0_1h": 1,  # Bullish
                "ADX_14_1h": 20  # Weak trend
            },
            "expected": {
                "allow_long": False,
                "allow_short": False,
                "trend_direction": "weak"
            }
        },
        {
            "name": "Neutral SuperTrend - strong ADX",
            "data": {
                "close": 1.95,
                "supertrend_10_3.0_1h": 1.95,
                "supertrend_direction_10_3.0_1h": 0,  # Neutral
                "ADX_14_1h": 30  # Strong trend
            },
            "expected": {
                "allow_long": False,
                "allow_short": False,
                "trend_direction": "neutral"
            }
        }
    ]
    
    print(f"  Testovanie {len(test_scenarios)} scenárov...")
    
    passed = 0
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n  Scenár {i}: {scenario['name']}")
        
        # Vytvorenie test row
        test_row = pd.Series(scenario['data'])
        
        # Vyhodnotenie filter
        result = evaluate_supertrend_adx_filter(test_row, trend_filter_config, log)
        
        # Kontrola výsledkov
        expected = scenario['expected']
        
        checks = [
            ("allow_long", result['allow_long'] == expected['allow_long']),
            ("allow_short", result['allow_short'] == expected['allow_short']),
            ("trend_direction", result['trend_direction'] == expected['trend_direction']),
            ("filter_active", result['filter_active'] == True)
        ]
        
        scenario_passed = all(check[1] for check in checks)
        
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"    {status} {check_name}: {result.get(check_name)} {'✓' if check_result else '✗'}")
        
        if scenario_passed:
            passed += 1
            print(f"    ✅ Scenár {i} prešiel")
        else:
            print(f"    ❌ Scenár {i} zlyhal")
            print(f"       Očakávané: {expected}")
            print(f"       Skutočné: {result}")
    
    print(f"\n  Výsledok: {passed}/{len(test_scenarios)} scenárov prešlo")
    return passed == len(test_scenarios)

def test_disabled_filter():
    """Test vypnutého filter"""
    print("\n🧪 Test: Vypnutý trend filter")
    
    trend_filter_config = {
        "enabled": False,
        "method": "supertrend_adx",
        "timeframe": "1h"
    }
    
    test_row = pd.Series({
        "close": 2.00,
        "supertrend_10_3.0_1h": 1.95,
        "supertrend_direction_10_3.0_1h": -1,  # Bearish
        "ADX_14_1h": 15  # Weak trend
    })
    
    result = evaluate_supertrend_adx_filter(test_row, trend_filter_config, log)
    
    # Keď je filter vypnutý, všetky trades by mali byť povolené
    expected_result = {
        'allow_long': True,
        'allow_short': True,
        'filter_active': False
    }
    
    checks = [
        ("allow_long", result['allow_long'] == expected_result['allow_long']),
        ("allow_short", result['allow_short'] == expected_result['allow_short']),
        ("filter_active", result['filter_active'] == expected_result['filter_active'])
    ]
    
    all_passed = all(check[1] for check in checks)
    
    for check_name, check_result in checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}: {result.get(check_name)}")
    
    if all_passed:
        print("  ✅ Vypnutý filter funguje správne")
    else:
        print("  ❌ Vypnutý filter nefunguje správne")
    
    return all_passed

def test_missing_data():
    """Test chýbajúcich dát"""
    print("\n🧪 Test: Chýbajúce dáta")
    
    trend_filter_config = {
        "enabled": True,
        "method": "supertrend_adx",
        "timeframe": "1h",
        "params": {
            "supertrend": {"atrPeriod": 10, "multiplier": 3.0},
            "adx": {"period": 14, "minAdx": 25}
        }
    }
    
    # Test s chýbajúcimi dátami
    test_row = pd.Series({
        "close": 2.00,
        # supertrend_10_3.0_1h: missing
        "supertrend_direction_10_3.0_1h": 1,
        "ADX_14_1h": 30
    })
    
    result = evaluate_supertrend_adx_filter(test_row, trend_filter_config, log)
    
    # Pri chýbajúcich dátach by mal filter povoliť všetky trades
    expected_result = {
        'allow_long': True,
        'allow_short': True,
        'filter_active': False,
        'trend_direction': 'missing_data'
    }
    
    checks = [
        ("allow_long", result['allow_long'] == expected_result['allow_long']),
        ("allow_short", result['allow_short'] == expected_result['allow_short']),
        ("filter_active", result['filter_active'] == expected_result['filter_active']),
        ("trend_direction", result['trend_direction'] == expected_result['trend_direction'])
    ]
    
    all_passed = all(check[1] for check in checks)
    
    for check_name, check_result in checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}: {result.get(check_name)}")
    
    if all_passed:
        print("  ✅ Handling chýbajúcich dát funguje správne")
    else:
        print("  ❌ Handling chýbajúcich dát nefunguje správne")
    
    return all_passed

def test_config_validation():
    """Test validácie konfigurácie"""
    print("\n🧪 Test: Validácia konfigurácie")
    
    # Test s neznámou metódou
    trend_filter_config = {
        "enabled": True,
        "method": "unknown_method",
        "timeframe": "1h"
    }
    
    test_row = pd.Series({
        "close": 2.00,
        "supertrend_10_3.0_1h": 1.95,
        "supertrend_direction_10_3.0_1h": 1,
        "ADX_14_1h": 30
    })
    
    result = evaluate_supertrend_adx_filter(test_row, trend_filter_config, log)
    
    # Pri neznámej metóde by mal filter povoliť všetky trades
    if result['allow_long'] and result['allow_short'] and not result['filter_active']:
        print("  ✅ Neznáma metóda správne handled")
        return True
    else:
        print("  ❌ Neznáma metóda nesprávne handled")
        print(f"     Výsledok: {result}")
        return False

def main():
    """Spustí všetky testy"""
    print("🚀 Spúšťam testy pre SuperTrend + ADX trend filter")
    print("=" * 60)
    
    tests = [
        test_supertrend_adx_filter_basic,
        test_disabled_filter,
        test_missing_data,
        test_config_validation
    ]
    
    passed = 0
    total = len(tests)
    
    try:
        for test_func in tests:
            if test_func():
                passed += 1
        
        print(f"\n{'='*60}")
        print(f"🎉 Výsledok: {passed}/{total} testov prešlo úspešne!")
        
        if passed == total:
            print("✅ SuperTrend + ADX trend filter je pripravený na použitie!")
            print("\n💡 Konfigurácia v strategyConfig_scalp_1s.json:")
            print('  "trendFilter": {')
            print('    "enabled": true,')
            print('    "method": "supertrend_adx",')
            print('    "timeframe": "1h",')
            print('    "params": {')
            print('      "supertrend": {"atrPeriod": 10, "multiplier": 3.0},')
            print('      "adx": {"period": 14, "minAdx": 25}')
            print('    }')
            print('  }')
        else:
            print("❌ Niektoré testy zlyhali - skontrolujte implementáciu")
        
    except Exception as e:
        print(f"❌ Chyba pri testovaní: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
