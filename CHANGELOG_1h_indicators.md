# Changelog: Pridanie 1h indikátorov do precompute_features.py

## Prehľad zmien

Pridaná podpora pre predpočítanie 1-hodinových (1h) indikátorov do `precompute_features.py` skriptu.

## Nové funkcie

### 1. Nová funkcia `add_1h_indicators_to_config()`
- Automaticky pridá 1h indikátory do konfigurácie
- Podporované 1h indikátory:
  - `atr_1h` - Average True Range (14 period)
  - `rsi_1h` - Relative Strength Index (14 period)
  - `ema_1h` - Exponential Moving Averages (9 a 21 period)
  - `adx_1h` - Average Directional Index (14 period)
  - `bollinger_1h` - Bollinger Bands (20 period, 2.0 std dev)
  - `vwap_1h` - Volume Weighted Average Price
  - `hmm_1h` - Hidden Markov Model (3 components, volatility-based)

### 2. Rozšírená funkcia `precompute_features_for_simulation()`
- Nový parameter `enable_1h_indicators: bool = False`
- Ak je True, automaticky pridá 1h indikátory do konfigurácie

### 3. Nový command-line argument
```bash
--enable-1h-indicators
```
- Pridá 1h indikátory do konfigurácie pri spustení skriptu

## Upravené funkcie

### 1. `validate_config_for_features()`
- Pridaná validácia pre 1h indikátory
- Automatické vytvorenie konfigurácie pre chýbajúce 1h indikátory

### 2. `fill_missing_features()`
- Rozšírený zoznam technických indikátorov o 1h varianty
- Pridané default hodnoty pre 1h indikátory

### 3. Načítanie dát
- Automatické načítanie 1h OHLCV dát ak nie sú primárnym timeframe-om
- Nová helper funkcia `load_ohlcv_timeframe()` pre efektívne načítanie rôznych timeframe-ov

### 4. Validácia výsledkov
- Rozšírený zoznam kľúčových features o 1h indikátory pre kontrolu

## Použitie

### Základné použitie (bez 1h indikátorov)
```bash
python precompute_features.py \
  --config temp_config_1s.json \
  --raw-data-dir raw_data \
  --output-dir parquet_processed \
  --start 2024-04-01 \
  --end 2024-04-30
```

### S povolenými 1h indikátormi
```bash
python precompute_features.py \
  --config temp_config_1s.json \
  --raw-data-dir raw_data \
  --output-dir parquet_processed \
  --start 2024-04-01 \
  --end 2024-04-30 \
  --enable-1h-indicators
```

### Programatické použitie
```python
from precompute_features import precompute_features_for_simulation

precompute_features_for_simulation(
    config_path="temp_config_1s.json",
    raw_data_dir="raw_data",
    output_dir="parquet_processed",
    start_date="2024-04-01",
    end_date="2024-04-30",
    enable_1h_indicators=True  # Nový parameter
)
```

## Príklady

### 1. `example_1h_indicators.py`
- Interaktívny skript pre demonštráciu použitia
- Zobrazuje zoznam 1h indikátorov, ktoré budú pridané
- Umožňuje spustenie s potvrdením používateľa

## Výhody 1h indikátorov

1. **Dlhodobejší kontext**: 1h indikátory poskytujú širší pohľad na trh
2. **Trend identifikácia**: Lepšie rozpoznávanie hlavných trendov
3. **Filtrovanie signálov**: Možnosť filtrovania krátkodobých signálov podľa dlhodobého trendu
4. **Risk management**: Lepšie posúdenie celkového rizika pozície

## Technické detaily

### Štruktúra dát
- 1h dáta sa načítavaju z `raw_data/ohlcv/1h/` adresára
- Výstupné features obsahujú suffix `_1h` pre rozlíšenie od 5m indikátorov

### Kompatibilita
- Spätne kompatibilné - existujúce konfigurácie fungujú bez zmien
- 1h indikátory sa pridávajú iba ak sú explicitne požadované

### Fallback mechanizmus
- Ak 1h dáta nie sú dostupné, použijú sa dáta z primárneho timeframe-u
- Automatické forward-fill a backward-fill pre chýbajúce hodnoty

## Budúce rozšírenia

Možné ďalšie vylepšenia:
- Podpora pre 4h, 1d timeframe-y
- Konfigurovateľné periody pre 1h indikátory
- Automatická detekcia dostupných timeframe-ov
- Optimalizácia výpočtu pre veľké datasety
